<template>
  <div id="header">
    <div class="option" v-for="(item, index) in title" :key="index">
      <div
        class="option-item"
        :class="{
          active: isActive === index,
          hasChildren: item.children,
          disabled: !item.isClick
        }"
        @click="handleItemClick(item, index)"
        @mouseenter="handleMouseEnter(index, item)"
        @mouseleave="handleMouseLeave"
      >
        <div class="option-item-icon"></div>
        <div class="option-item-text" :title="item.text">{{ item.text }}</div>
        <!-- 下拉箭头 -->
        <div v-if="item.children" class="arrow" :class="{ expanded: showSubmenu === index }">
          <svg width="12" height="8" viewBox="0 0 12 8" fill="currentColor">
            <path d="M6 8L0 0h12L6 8z"/>
          </svg>
        </div>

        <!-- 子菜单 -->
        <transition name="submenu-fade">
          <div v-if="item.children && showSubmenu === index" class="submenu">
            <div
              class="submenu-item"
              v-for="(sub, subIndex) in item.children"
              :key="subIndex"
              :class="{ disabled: !sub.isClick }"
              :title="sub.text"
              @click.stop="changeActive(sub, index)"
            >
              {{ sub.text }}
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Header",
  props: {
    title: {
      type: Array,
    },
  },
  data() {
    return {
      isActive: 0,
      showSubmenu: null,
      submenuTimer: null,
    };
  },
  mounted() {
    this.setActiveByRoute();
  },
  watch: {
    $route() {
      this.setActiveByRoute();
    },
  },
  methods: {
    setActiveByRoute() {
      this.title.forEach((item, index) => {
        if (item.path === this.$route.name) {
          this.isActive = index;
        } else if (item.children) {
          const match = item.children.find(
            (sub) => sub.path === this.$route.name
          );
          if (match) this.isActive = index;
        }
      });
    },
    handleItemClick(item, index) {
      if (item.children) {
        // 有子菜单的项目，切换子菜单显示状态
        this.showSubmenu = this.showSubmenu === index ? null : index;
      } else {
        this.changeActive(item, index);
      }
    },
    handleMouseEnter(index, item) {
      if (item.children) {
        // 清除之前的定时器
        if (this.submenuTimer) {
          clearTimeout(this.submenuTimer);
        }
        this.showSubmenu = index;
      }
    },
    handleMouseLeave() {
      // 延迟隐藏子菜单，提供更好的用户体验
      this.submenuTimer = setTimeout(() => {
        this.showSubmenu = null;
      }, 150);
    },
    changeActive(item, parentIndex) {
      if (!item.isClick) {
        this.$message.warning("暂未开放");
        return;
      }
      this.isActive = parentIndex;
      this.showSubmenu = null; // 隐藏子菜单
      this.$router.push({ name: item.path });
    },
  },
};
</script>
<style lang="scss" scoped>
#header {
  width: 100%;
  display: flex;
  justify-content: center;
  background-size: 100% 100%;
  margin-top: 24px;
  margin-bottom: 16px;
  position: relative;
  z-index: 100;

  .option {
    padding-bottom: 28px;
    margin-right: 40px;

    &:last-child {
      margin-right: 0;
    }

    .option-item {
      position: relative;
      display: flex;
      align-items: center;
      height: 44px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 6px;
      padding: 0 8px;
      transition: all 0.2s ease-in-out;

      // 确保父容器建立层叠上下文
      &.hasChildren {
        z-index: 1;
      }

      &:hover {
        transform: translateY(-1px);
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;

        &:hover {
          transform: none;
        }
      }

      .option-item-icon {
        width: 44px;
        height: 44px;
        background-size: 100% 100%;
        transition: transform 0.2s ease-in-out;
      }

      .option-item-text {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.02em;
        padding-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
        transition: all 0.2s ease-in-out;
      }

      .arrow {
        margin-left: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        transition: transform 0.2s ease-in-out;

        &.expanded {
          transform: rotate(180deg);
        }

        svg {
          width: 12px;
          height: 8px;
        }
      }

      .submenu {
        position: absolute;
        top: calc(100% + 8px);
        left: 10px;
        min-width: 140px;
        border: 1px solid #ddd;
        border-radius: 8px;
        z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24), 0 4px 16px rgba(0, 0, 0, 0.12);
        overflow: hidden;
        font-family: Source Han Sans;
        font-size: 14px;

        // 确保子菜单始终在最上层
        transform: translateZ(0);
        will-change: transform, opacity;

        .submenu-item {
          padding: 12px 16px;
          cursor: pointer;
          white-space: nowrap;
          transition: all 0.15s ease-in-out;
          border-bottom: 1px solid rgba(0, 0, 0, 0.06);
          display: block;
          width: 100%;
          box-sizing: border-box;
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &.disabled {
            cursor: not-allowed;
            opacity: 0.5;
          }

          &:not(.disabled):hover {
            transform: translateX(4px);
          }
        }
      }
    }
  }
}

// 子菜单动画
.submenu-fade-enter-active,
.submenu-fade-leave-active {
  transition: all 0.2s ease-in-out;
}

.submenu-fade-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.submenu-fade-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

[data-theme="tint"] .option-item {
  &:hover:not(.disabled) {
    background-color: rgba(22, 119, 255, 0.08);

    .option-item-text {
      color: #1677ff;
    }

    .option-item-icon {
      transform: scale(1.05);
    }
  }

  &.active {
    .option-item-icon {
      background-image: url("@/assets/tableicon/headeritem-tinticon-active.png");
      transform: scale(1.1);
    }

    .option-item-text {
      color: #f5222d;
      font-weight: 600;
    }
  }
}

[data-theme="dark"] .option-item {
  &:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.08);

    .option-item-text {
      color: #fff !important;
    }

    .option-item-icon {
      transform: scale(1.05);
    }
  }

  &.active {
    .option-item-icon {
      background-image: url("@/assets/tableicon/headeritem-darkicon-active.png");
      transform: scale(1.1);
    }

    .option-item-text {
      background: linear-gradient(180deg, #ffffff 0%, #ff7463 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      font-weight: 600;
    }
  }
}

[data-theme="tint"] #header {
  background-image: url("@/assets/tableicon/header-tintbg.png");

  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-tinticon.png");
  }

  .option-item-text {
    color: rgba(0, 0, 0, 0.75);
  }

  .arrow {
    color: rgba(0, 0, 0, 0.65);
  }

  .submenu {
    background-color: #ecf7ff;
    color: rgba(0, 0, 0, 0.75);
    border-color: #6ab2ff !important;
    backdrop-filter: blur(8px);

    .submenu-item {
      &:hover:not(.disabled) {
        background-color: #1c387c !important;
        color: #fff !important;

        // 使用伪元素创建完全覆盖的背景
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -20px;
          right: -20px;
          bottom: 0;
          background-color: #1c387c;
          z-index: -1;
        }
      }

      &.disabled {
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }
}

[data-theme="dark"] #header {
  background-image: url("@/assets/tableicon/header-darkbg.png");

  .option-item-icon {
    background-image: url("@/assets/tableicon/headeritem-darkicon.png");
  }

  .option-item-text {
    color: #2ac3ff;
  }

  .arrow {
    color: #2ac3ff;
  }

  .submenu {
    background-color: rgba(27, 34, 66, 0.95);
    color: #2ac3ff;
    border-color: #327dff !important;
    backdrop-filter: blur(8px);

    .submenu-item {
      &:hover:not(.disabled) {
        background-color: #1c387c !important;
        color: #fff !important;

        // 使用伪元素创建完全覆盖的背景
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -20px;
          right: -20px;
          bottom: 0;
          background-color: #1c387c;
          z-index: -1;
        }
      }

      &.disabled {
        color: rgba(42, 195, 255, 0.35);
      }
    }
  }
}
</style>
