<template>
  <div class="testChemicals">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      ></OgwSearch>
      <div class="btn-group">
        <el-button type="primary" @click="addRow">新增</el-button>
      </div>
    </div>
    <div class="table-container">
      <OgwTable
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :merge-keys="['deviceNameCode']"
        :show-index="true"
        :page-size="10"
        :current-page="1"
        :showActions="true"
        @cell-click="handleCellClick"
      >
        <template #state="{ row, $index }">
          <div v-if="row.state === 1" class="saved">已保存</div>
          <div v-else-if="row.state === 2" class="submitted">已提交</div>
          <div v-else>未保存</div>
        </template>

        <template #actions="{ row, $index }">
          <el-button type="text" size="mini" @click="saveRow(row)"
            >保存</el-button
          >
          <el-button type="text" size="mini" @click="deleteRow(row, $index)"
            >删除</el-button
          >
          <el-button type="text" size="mini" @click="submitRow(row)"
            >提交</el-button
          >
        </template>
      </OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  addTestChemical,
  getTestChemicals,
  saveTestChemical,
  deleteTestChemical,
} from "@/api/ogwActiveRecord/testChemicals.js";
import { getProd } from "@/api/common.js";

export default {
  name: "testChemicals",
  components: {
    OgwSearch,
    OgwTable,
  },
  created() {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth() 返回 0-11，需要 +1
    const dateArr = [];
    dateArr[0] = currentYear + "-" + String(1).padStart(2, '0'); // 年初：01月
    dateArr[1] = currentYear + "-" + String(currentMonth).padStart(2, '0'); // 当前月
    this.searchForm.monthRange = dateArr;
  },
  mounted() {
    this.getTableData();
    this.getProdList();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ];
    },
  },
  data() {
    return {
      loading: false,
      orgList: [],
      deviceOptions: [],
      platSelOptions: [],
      searchForm: {
        orgId: "",
        deviceNameCode: "",
        monthRange: [],
      },
      columns: [
        {
          label: "平台名称",
          prop: "productionName",
          editable: true,
          options: [],
        },
        { label: "项目名称", prop: "projectName", editable: true },
        { label: "供应商", prop: "supplier", editable: true },
        {
          label: "服务时间",
          prop: "serviceTime",
          editable: true,
          formatter: (row) => {
            return row?.serviceTime?.split(" ")[0];
          },
          editType: "date",
          dateConfig: {
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择时间",
          },
        },
        { label: "状态", prop: "state" },
        { label: "创建时间", prop: "createTime" },
      ],
      tableData: [],
    };
  },
  methods: {
    handleCellClick({ row, prop, index, value }) {
      console.log(`点击了第 ${index + 1} 行的 ${prop} 列，值为：`, value);
      // 可执行跳转、弹窗、编辑等操作
    },
    addRow() {
      // 新增空白行
      this.tableData.push({
        productionName: "",
        projectName: "",
        supplier: "",
        serviceTime: "",
        state: "",
      });
    },
    saveRow(row) {
      // console.log("row", row);
      this.saveTableRow(row);
    },
    submitRow(row) {
      // console.log("row", row);
      this.addTableRow(row);
    },
    deleteRow(row, index) {
      if (row.id) {
        this.deleteTableRow({ id: row.id });
      } else {
        this.tableData.splice(index, 1);
      }
    },
    handleSearch(value) {
      this.searchForm.orgId = value?.orgId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.monthRange = value.monthRange;
      this.getTableData();
    },
    handleReset(value) {
      // 重置后重新设置默认的月份范围
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      const dateArr = [];
      dateArr[0] = currentYear + "-" + String(1).padStart(2, '0');
      dateArr[1] = currentYear + "-" + String(currentMonth).padStart(2, '0');

      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
        monthRange: dateArr,
      };
      this.getTableData();
    },
    async getTableData() {
      this.loading = true;
      try {
        const data = {
          hzNo: this.searchForm.deviceNameCode || "",
          startDate: this.searchForm.monthRange[0] || null,
          endDate: this.searchForm.monthRange[1] || null,
        };
        const res = await getTestChemicals(data);
        if (res.code === 200) {
          this.tableData = res.data;
        }
      } catch (error) {
        console.log("获取数据失败", error);
      } finally {
        this.loading = false;
      }
    },
    async saveTableRow(data) {
      const res = await saveTestChemical(data);
      if (res.code === 200) {
        this.$message.success("保存成功");
        this.getTableData();
      }
    },
    async addTableRow(data) {
      const res = await addTestChemical(data);
      if (res.code === 200) {
        this.$message.success("新增成功");
        this.getTableData();
      }
    },
    async deleteTableRow(data) {
      const res = await deleteTestChemical(data);
      if (res.code === 200) {
        this.$message.success("删除成功");
        this.getTableData();
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
      }
      this.platSelOptions = res.data.map((item) => {
        return item.children.map((child) => {
          return { label: child.name, value: child.hzNo };
        });
      });
      this.searchForm.orgId = this.orgList[0]?.orgId;
      this.columns[0].options = this.platSelOptions.flat();
    },
  },
};
</script>
<style lang="scss" scoped>
.testChemicals {
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-group {
      margin-bottom: 10px;
      text-align: right;
    }
  }
  .table-container {
    padding: 10px;
  }
}

.submitted {
  color: #00b42a;
}

.saved {
  color: #1677ff;
}

[data-theme="dark"] .testChemicals{
  background: #162549;
}

[data-theme="tint"] .testChemicals {
  background: #fff;
}
</style>
