<template>
  <div class="oilProduct">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="comparison-box">
        <chartBox :title="'分油气田对比'">
          <template v-slot:box-right>
            <!-- 下拉选择框 -->
            <el-select v-model="value" placeholder="请选择">
              <el-option label="总产量" value="all"> </el-option>
              <el-option label="净产量" value="all1"> </el-option>
              <el-option label="净销量" value="all2"> </el-option>
            </el-select>
          </template>
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <ComparisonChart />
        </chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'分油气田预算完成率'">
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <CompletChart />
        </chartBox>
      </div>
      <div class="trend-box">
        <chartBox :title="'趋势变动'">
          <div><CarouselBtn :buttons="buttons" /></div>
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <TrendChart />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";
import ComparisonChart from "./comparisonChart/index.vue";
import CompletChart from "./completChart/index.vue";
import TrendChart from "./trendChart/index.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
export default {
  name: "oilProduct",
  components: {
    ItemCard,
    ComparisonChart,
    CompletChart,
    CommonTable,
    TrendChart,
    CarouselBtn,
    DatePicker,
  },
  data() {
    return {
      newDateValue: "",
      value: "",
      medicine: [
        {
          value: "all",
          label: "油气合计",
        },
        {
          value: "gas",
          label: "天然气",
        },
        {
          value: "oil",
          label: "凝析油",
        },
        {
          value: "condensate",
          label: "原油",
        },
      ],
      radio: "all",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气总产量", value: "100" },
        { title: "天然气总产量", value: "100" },
        { title: "石油液体总产量", value: "100" },
      ],
      colums: [
        {
          label: "指标",
          prop: "indicator",
        },
        {
          label: "本年累计",
          prop: "currentYear",
        },
        {
          label: "同期挑战目标",
          prop: "challengeTarget",
        },
        {
          label: "同期目标完成率",
          prop: "completionRate",
        },
        {
          label: "全年挑战目标",
          prop: "yearChallengeTarget",
        },
        {
          label: "全年目标完成率",
          prop: "yearCompletionRate",
        },
      ],
      tableData: [
        {
          indicator: "油气总产量",
          currentYear: "10000",
          challengeTarget: "10000",
          completionRate: "100%",
          yearChallengeTarget: "10000",
          yearCompletionRate: "100%",
        },
        {
          indicator: "天然气总产量",
          currentYear: "10000",
          challengeTarget: "10000",
          completionRate: "100%",
          yearChallengeTarget: "10000",
          yearCompletionRate: "100%",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.oilProduct {
  height: calc(100vh - 84px); // 减去Header高度
  display: flex;
  flex-direction: column;
  overflow: auto; // 改为auto，允许整体页面滚动以防内容过多
  padding: 4px; // 添加少量内边距

  .content-up {
    display: flex;
    justify-content: space-between;
    height: 50%; // 调整为50%，为下半部分留出更多空间
    min-height: 320px; // 减少最小高度，确保表格能完整显示
    max-height: 420px; // 减少最大高度限制

    .main-indicators {
      flex: 1;
      min-width: 0;
      margin-right: 10px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .statistics-box {
      flex: 1;
      min-width: 0;
      height: 100%;
      display: flex;
      flex-direction: column;

      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        overflow: visible; // 移除滚动条，让表格完整显示
        display: flex;
        flex-direction: column;
      }
    }
  }

  .card-box {
    display: flex;
    justify-content: space-between;
    margin: 8px 20px; // 减少边距，避免内容溢出
    flex: 1; // 占据剩余空间
    min-height: 0; // 允许flex收缩
    gap: 12px; // 使用gap替代margin-right

    .item-card {
      flex: 1;
      min-width: 0; // 允许收缩
      height: 100%; // 确保卡片填满容器高度
    }
  }

  .content-down {
    display: flex;
    gap: 10px; // 所有容器之间统一间隔
    margin-top: 6px; // 进一步减少顶部边距
    flex: 1; // 下半部分占剩余空间
    min-height: 300px; // 设置最小高度确保图表能完整显示
    // 移除最大高度限制，让图表有足够空间显示

    .comparison-box,
    .completion-rate,
    .trend-box {
      flex: 1;
      min-width: 0;
      height: 100%; // 确保子容器充满高度
      display: flex;
      flex-direction: column;
      min-height: 300px; // 确保每个图表容器有足够高度

      .medice-radio {
        margin-top: 8px; // 进一步减少顶部边距
        margin-bottom: 4px; // 减少底部边距
        padding-left: 16px;
        flex-shrink: 0; // 防止单选按钮被压缩

        .el-radio {
          margin-right: 16px;
        }
      }
    }
  }
}

// 优化图表容器，确保图表完整显示
::v-deep .content-down {
  .chart-content {
    overflow: visible !important; // 确保图表内容不被截断
  }
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}

::v-deep .el-select {
  width: 120px;
  margin-right: 12px;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: none;
  }
  .el-input__suffix {
    line-height: 46px;
  }
}
</style>
