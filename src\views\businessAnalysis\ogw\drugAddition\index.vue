<template>
  <div class="drugAddition">
    <CommonTable
      :tableData="tableData"
      :colums="colums"
      :border="false"
      :total="total"
      :page-size="pageSize"
      :current-page="currentPage"
      @page-size-change="handleSizeChange"
      @current-page-change="handleCurrentChange"
    >
      <template #statuSlot="{ scope }">
        <div
          class="statusItem"
          :class="{
            error: scope.row.state == 2,
            normal: scope.row.state == 1,
          }"
        >
          <span>{{ scope.row.state === 2 ? "异常" : "正常" }}</span>
        </div>
      </template>
    </CommonTable>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import { getDrugAddition } from "@/api/ogw/index.js";
export default {
  name: "drugAddition",
  components: {
    CommonTable,
  },
  props: {
    date: {
      type: String,
    },
  },
  watch: {
    date: {
      handler() {
        this.getTableData();
      },
    },
    allTableData: {
      handler() {
        this.total = this.allTableData.length;
      },
      deep: true,
    },
    paginatedTableData: {
      handler() {
        this.tableData = this.paginatedTableData;
      },
      deep: true,
    },
  },
  mounted() {
    this.getTableData();
  },
  computed: {
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allTableData.slice(start, end);
    },
  },
  data() {
    return {
      pageSize: 5,
      currentPage: 1,
      total: 0,
      allTableData: [],
      tableData: [],
      colums: [
        { prop: "productionName", label: "平台名称" },
        { prop: "chemicalName", label: "药剂名称" },
        {
          prop: "ratio",
          label: "(加注量+库存量)/入库量",
          textColor: "#1DFBFD",
        },
        {
          prop: "state",
          label: "状态",
          align: "center",
          slotName: "statuSlot",
        },
      ],
    };
  },
  methods: {
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1; // 重置到第一页
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
    },
    async getTableData() {
      const res = await getDrugAddition(this.date);
      if (res.code === 200) {
        this.allTableData = res.data || [];
        this.total = this.allTableData.length;
        this.currentPage = 1;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.drugAddition {
  margin: 16px;
  .statusItem {
    display: inline-block;
    width: 84px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-size: 100% 100%;
    padding-left: 22px;
  }
}

[data-theme="dark"] .normal {
  background-image: url("@/assets/tableicon/normal-darkicon.png");
}
[data-theme="dark"] .error {
  background-image: url("@/assets/tableicon/error-darkicon.png");
}
[data-theme="tint"] .normal {
  background-image: url("@/assets/tableicon/normal-tinticon.png");
}
[data-theme="tint"] .error {
  background-image: url("@/assets/tableicon/error-tinticon.png");
}
</style>
