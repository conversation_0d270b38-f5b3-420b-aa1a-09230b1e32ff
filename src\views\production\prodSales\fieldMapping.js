/**
 * 数据字段中文映射字典
 * 用于将后端返回的英文字段名转换为用户友好的中文名称
 *
 * 使用说明：
 * 1. 当后端返回新的字段时，只需在此处添加对应的映射关系
 * 2. 映射格式：英文字段名 -> 中文显示名称
 * 3. 如果字段不在映射中，将显示原始字段名
 */

// 字段名称映射字典
export const fieldNameMapping = {
  "ls172": "陵水17-2",   // 陵水17-2气田
  "ls251": "陵水25-1",   // 陵水25-1气田
  "yc131": "崖城13-1",   // 崖城13-1气田
  "yc1310": "崖城13-10" // 崖城13-10气田
};

// 机构名称映射字典（用于属性名清理）
export const organizationMapping = {
  '香港中电': 'hk_power',
  '气电南山': 'nanshan_gas',
  '气电广东': 'guangdong_gas'
};

/**
 * 将英文字段名转换为中文名称
 * @param {string} fieldKey - 英文字段名
 * @returns {string} 中文字段名或原始字段名（如果映射不存在）
 */
export function getChineseFieldName(fieldKey) {
  return fieldNameMapping[fieldKey] || fieldKey;
}

/**
 * 将中文机构名转换为属性名
 * @param {string} orgName - 机构名称
 * @returns {string} 清理后的属性名
 */
export function getOrganizationProp(orgName) {
  return organizationMapping[orgName] || orgName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').toLowerCase();
}

/**
 * 获取所有支持的字段列表
 * @returns {Array} 字段列表
 */
export function getSupportedFields() {
  return Object.keys(fieldNameMapping);
}

/**
 * 检查字段是否在映射字典中
 * @param {string} fieldKey - 字段名
 * @returns {boolean} 是否存在映射
 */
export function hasFieldMapping(fieldKey) {
  return fieldKey in fieldNameMapping;
}

// 默认导出整个映射对象
export default {
  fieldNameMapping,
  organizationMapping,
  getChineseFieldName,
  getOrganizationProp,
  getSupportedFields,
  hasFieldMapping
};
