<template>
  <div class="item-card">
    <div class="title-box">
      {{ info.title }}
    </div>
    <div class="content-box">
      <div class="amount">
        <span class="num">{{ info.value || '100' }}</span>
        <span class="unit">{{ info.unit || '万吨' }}</span>
      </div>
      <div class="img-box"></div>
      <div class="rate-box" v-if="info.rates && info.rates.length">
        <div class="rate-item" v-for="rate in info.rates" :key="rate.label">
          <span class="label">{{ rate.label }}</span>
          <span class="value">{{ rate.value || rate.percent }}</span>
        </div>
      </div>
      <div class="rate-box" v-else>
        <div class="rate-item">
          <span class="label">同期预算执行</span>
          <span class="value">10%</span>
        </div>
        <div class="rate-item">
          <span class="label">同比</span>
          <span class="value">10%</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "ItemCard",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
};
</script>
<style lang="scss" scoped>
.item-card {
  width: 100%;
  height: 100%; // 添加固定高度
  box-sizing: border-box;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止内容溢出

  .title-box {
    height: 40px;
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; // 防止标题被压缩
  }

  .content-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    flex: 1; // 占据剩余空间
    padding: 8px; // 添加内边距
    min-height: 0; // 允许flex收缩

    .amount {
      display: flex;
      align-items: baseline;
      margin-bottom: 8px;

      .num {
        font-family: D-DIN;
        font-size: 36px; // 稍微减小字体，避免溢出
        font-weight: bold;
        margin-right: 8px;
      }
      .unit {
        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: bold;
      }
    }

    .img-box {
      width: 80px; // 减小图片尺寸
      height: 60px;
      background-size: 100% 100%;
      margin-bottom: 8px;
      flex-shrink: 0;
    }

    .rate-box {
      width: 80%; // 增加宽度利用率
      flex-shrink: 0;

      .rate-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px; // 减少间距
        font-family: Source Han Sans;
        font-size: 12px; // 稍微减小字体

        .value{
          font-size: 16px; // 稍微减小字体
          font-weight: bold;
        }
      }
    }
  }
}

[data-theme="dark"] .item-card {
  background: #1a2e5e;
  border: 1px solid rgba(23, 131, 255, 0.5);
  .title-box {
    background: rgba(27, 126, 242, 0.16);
    color: #fff;
  }
  .amount {
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    background: linear-gradient(180deg, #ffffff 0%, #75d8ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  .img-box {
    background-image: url("@/assets/tableicon/oilcard-darkicon.png");
  }
  .rate-box {
    color: #fff;
  }
}
</style>
