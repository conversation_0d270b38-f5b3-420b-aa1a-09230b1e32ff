import Vue from "vue";
import VueRouter from "vue-router";

// @ts-ignore
import operating from "./operating";
import { cockpitRouter, singleRouter } from "./business";
import ogw from "./ogw";
import revenue from "./revenue";

Vue.use(VueRouter);

const originalPush = VueRouter.prototype.push;
// @ts-ignore
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  // @ts-ignore
  return originalPush.call(this, location).catch((err) => err);
};

export const routes = [
  operating,
  cockpitRouter,
  singleRouter,
  ogw,
  revenue,
  {
    path: "*",
    name: "404",
    component: {
      render: (h: any) => h("h1", "404"),
    },
  },
];

const handleTheme = () => {
  const theme = window.document.documentElement.getAttribute("data-theme");
  if (theme !== "dark" && theme !== "tint") {
    window.document.documentElement.setAttribute("data-theme", "dark");
  }
};

const beforeEach = (to: any, from: any, next: any) => {
  handleTheme();
  next();
};

export const router = (base: string): VueRouter => {
  const Router = new VueRouter({
    mode: "history",
    base: base,
    routes,
  });

  // console.log('Router', Router);

  Router.beforeEach(beforeEach);

  return Router;
};
