@import "./mixin.scss";
@import "./config.scss";
@import './fast.scss';
@import './cfbgcui-reset.scss';
@import './themeTint.scss';
$grayBg: #f5f5f5;
$themeBg: #fff;
$themeColor: #1890ff;

$shadowColor: #ddd;

$nameColor: rgba(0, 0, 0, 0.85);
$titleColor: #333;
$paragraphColor: #666;
$tipColor: #999;

$boderColor: #DEDEDE;

$sucessColor: #52C41A;

/*
解决原项目样式偏差
*/
canvas {
    left: 0;
}

//
.ant-message {
    svg {
        font-size: 1.2rem;
    }
}


/*css 样式重置*/
html,
body {
    height: 100%;
    width: 100%;
}

body {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #080f26;
}

html,
body,
div,
span,
applet,
object,


h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    // font: inherit;
    font-weight: normal;
    vertical-align: baseline;
    box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul,
li {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

th,
td {
    vertical-align: middle;
}

/* custom */
a {
    outline: none;
    color: #307bfb;
    text-decoration: none;
    -webkit-backface-visibility: hidden;
}

a:focus {
    outline: none;
}

input:focus,
select:focus,
textarea:focus {
    outline: -webkit-focus-ring-color auto 0;
}


/*修改滚动条样式*/
div::-webkit-scrollbar,
body::-webkit-scrollbar,
ul::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    cursor: pointer;
}

div::-webkit-scrollbar-track,
body::-webkit-scrollbar-track,
ul::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0);
    border-radius: 2px;
}

div::-webkit-scrollbar-corner,
body::-webkit-scrollbar-corner,
ul::-webkit-scrollbar-corner {
    background: rgba(0, 0, 0, 0);
}

div::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb,
ul::-webkit-scrollbar-thumb {
    background: #707070;
    border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover,
body::-webkit-scrollbar-thumb:hover,
ul::-webkit-scrollbar-thumb:hover {
    background: #bebebe;
    width: 10px;
    transition: all .3s;
    cursor: pointer;
}


#app {
    position: relative;
    height: 100%;
    width: 100%;
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: left;
    display: flex;
    flex-direction: row;
    @include flex-row-start-start;
    overflow: hidden;
}

/* element 样式覆盖 */
.el-table thead {
    // color: #909399;
    color: #333;
}

.el-table th.el-table__cell {
    font-weight: bold !important;
    background: #EFF4F8;
    // padding: 0;
}

.el-table .el-table__cell {
    padding: 5px 0;
}

.el-pagination {
    display: flex;
    justify-content: flex-end;
}

.el-date-editor .el-range-separator {
    width: 20px;
}





.el-table .customCell {
    font-weight: bold !important;
    background: #EFF4F8;
}

/* ui库样式覆盖 */
.cfbgc-modal {
    padding: 0;
    border-radius: 10px;
    overflow: hidden;
}

.cfbgc-modal-title {
    position: relative;
    padding-left: 10px;
    display: flex;
    align-items: center;
}

.cfbgc-modal-title::before {
    content: '';
    position: absolute;
    left: 0;
    width: 5px;
    height: 19px;
    background: #3578F6;
    border-radius: 3px;
}

.cfbgc-modal-header {
    border-bottom: none;
}

.cfbgc-modal-footer {
    border-top: none;
    text-align: center;

    .cancelBtn {
        color: white;
        background: #BBC7D9;
    }

    .cancelBtn:hover {
        border-color: #BBC7D9;
    }
}

.cfbgc-popover-inner {
    padding: 5px;
}


/*
子应用页面 通用样式，布局
*/
.childContent {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 10px;

    .leftBox {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        margin-right: 10px
    }

    .rightBox {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
}

.formItem {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    .label {
        width: 120px;
        font-size: 14px;
    }

    .value {
        flex: 1;
    }
}

.contentBox {
    display: flex;
    flex: 1;
    overflow: hidden;

    .blockItem {
        display: flex;
        flex-direction: column;
        padding: 10px;
        overflow: hidden;
        background: #ffffff;
        border-radius: 6px;

        .blockTitle {
            display: flex;
            align-items: center;
            color: #1D1F24;
            font-size: 17px;
            margin-bottom: 10px;
            white-space: nowrap;
        }

        .shortLine {
            width: 5px;
            height: 19px;
            background: #3578F6;
            border-radius: 3px;
            margin-right: 10px;
        }

        .chartBox {
            flex: 1;
            overflow: hidden;

            .chart {
                flex: 1;
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
        }
    }
}

.filterContent {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .filterTitle {
        font-size: 18px;
        font-weight: bold;
        color: #1D1F24;
        white-space: nowrap;
        margin-right: 100px;
    }

    .filterItem {
        margin-right: 20px;
        display: flex;
        align-items: center;

        .label {
            max-width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .value {
            width: auto;
        }
    }
}

/* cf-ui 主题样式覆盖 深色*/
html[data-theme='dark'] {

    /* element 主题样式 */
    // 日期输入
    .el-input__inner {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .el-range-input {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .el-picker-panel {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .el-popper[x-placement^=bottom] .popper__arrow {
        border-bottom-color: #4F98F6;
    }

    .el-popper[x-placement^=bottom] .popper__arrow::after {
        border-bottom-color: #1A2E52;
    }

    .el-date-table th {
        color: white;
    }

    .el-picker-panel__icon-btn {
        color: white;
    }

    .el-date-table td.in-range div,
    .el-date-table td.in-range div:hover,
    .el-date-table.is-week-mode .el-date-table__row.current div,
    .el-date-table.is-week-mode .el-date-table__row:hover div {
        background-color: #4F98F6;
        color: white;
    }

    .el-date-range-picker__content.is-left {
        border-color: #4F98F6;
    }

    // 下拉
    .el-select-dropdown,
    .el-cascader__dropdown {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .el-select-dropdown__item {
        color: white;
        font-size: 14px !important;
    }

    .el-select-dropdown__item.selected {
        color: #4F98F6;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background: #606b78;
    }

    .el-cascader-node {
        color: white;
    }

    .el-cascader-node:not(.is-disabled):focus,
    .el-cascader-node:not(.is-disabled):hover {
        background: #4F98F6;
    }

    .el-cascader-menu {
        border-color: #4F98F6
    }

    // 多选
    .el-checkbox__inner {
        background: #1A2E52;
        border-color: #4F98F6;
    }

    // 表格
    .el-table thead {
        color: white;
    }

    .el-table th.el-table__cell {
        background: #1A2E5E;
    }

    .el-table tr {
        background: #1A2E52;
        color: white;
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
        border-color: #4F98F6;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
        background-color: #606b78;
    }

    .el-table--border,
    .el-table--group {
        border-color: #4F98F6;
        background-color: #1A2E52;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
        background-color: #4F98F6;
    }

    .el-table .customCell {
        background: #2465AF;
    }

    .el-table--border th.el-table__cell,
    .el-table__fixed-right-patch {
        border-color: #4F98F6;
    }

    // 分页
    .el-pagination__total {
        color: white;
    }

    .el-pager li {
        background: #4F98F6;
        color: white;
    }

    .el-pagination .btn-next,
    .el-pagination .btn-prev {
        background: #4F98F6;
        color: white;
    }

    .el-pagination button:disabled {
        background-color: #606b78;
    }

    // popover
    .el-popover {
        background: #1A2E52;
        border-color: #4F98F6;
    }

    // 输入框相关
    .cfbgc-select-selection,
    .cfbgc-input {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .cfbgc-input-affix-wrapper .cfbgc-input-prefix,
    .cfbgc-input-affix-wrapper .cfbgc-input-suffix {
        color: white;
    }

    .cfbgc-input-clear-icon {
        color: white;
    }

    .cfbgc-input-clear-icon:active {
        color: white;
    }

    // 下拉选择
    .cfbgc-select-dropdown-menu-item {
        color: white;
    }

    .cfbgc-select-dropdown-menu-item-selected {
        background-color: #1A2E52;
        color: white;
    }

    .cfbgc-select-dropdown-menu {
        background-color: #2d3755;
        border: none;
        // background-color: #1A2E52;
        // border: 1px solid #4F98F6;
    }

    .cfbgc-select-dropdown-menu-item:hover:not(.cfbgc-select-dropdown-menu-item-disabled) {
        background-color: #3d4a6f;
    }

    .cfbgc-select-dropdown-menu-item-active:not(.cfbgc-select-dropdown-menu-item-disabled) {
        background-color: #3d4a6f;
    }

    // 多选框
    .cfbgc-checkbox-inner {
        background-color: transparent;
    }

    .cfbgc-checkbox-checked .cfbgc-checkbox-inner {
        background-color: #2F6EDD;
    }

    // 日期相关
    .cfbgc-calendar-picker-clear {
        // background-color: transparent;
    }

    .cfbgc-calendar-picker-clear:hover {
        color: white;
    }

    .cfbgc-calendar {
        background: #1A2E52;
        color: white;
        border-color: #4F98F6;
    }

    .cfbgc-calendar-input-wrap {
        border-color: #4F98F6;
    }

    .cfbgc-calendar-range .cfbgc-calendar-body,
    .cfbgc-calendar-range .cfbgc-calendar-month-panel-body,
    .cfbgc-calendar-range .cfbgc-calendar-year-panel-body,
    .cfbgc-calendar-range .cfbgc-calendar-decade-panel-body {
        border-color: #4F98F6;
    }

    .cfbgc-calendar-range .cfbgc-calendar-input,
    .cfbgc-calendar-range .cfbgc-calendar-time-picker-input {
        background-color: #1A2E52;
        color: white;
    }

    .cfbgc-calendar-header .cfbgc-calendar-century-select,
    .cfbgc-calendar-header .cfbgc-calendar-decade-select,
    .cfbgc-calendar-header .cfbgc-calendar-year-select,
    .cfbgc-calendar-header .cfbgc-calendar-month-select {
        color: white;
    }

    .cfbgc-calendar-date {
        color: white;
    }

    .cfbgc-calendar-date:hover {
        background-color: #606b78;
    }

    .cfbgc-calendar-range .cfbgc-calendar-in-range-cell::before {
        background-color: #2465AF;
    }

    .cfbgc-calendar-header {
        border-color: #4F98F6;
    }

    .cfbgc-calendar-footer {
        border-color: #4F98F6;
    }

    .cfbgc-calendar-selected-day .cfbgc-calendar-date {
        background: #2F6EDD;
    }

    // 树列表
    .cfbgc-tree li .cfbgc-tree-node-content-wrapper {
        color: white;
    }

    .cfbgc-tree li .cfbgc-tree-node-content-wrapper:hover {
        background-color: #606b78;
    }

    .cfbgc-tree li .cfbgc-tree-node-content-wrapper.cfbgc-tree-node-selected {
        background-color: #606b78;
    }

    // 表格
    .cfbgc-table-header {
        background: #2465AF;
    }

    .cfbgc-table-fixed-header>.cfbgc-table-content>.cfbgc-table-scroll>.cfbgc-table-body {
        background: #1A2E52;
    }

    .cfbgc-table-thead>tr>th {
        background: #1b2242;
        border-color: #4F98F6;
        color: #CCE4FF;
    }

    .cfbgc-table-bordered .cfbgc-table-tbody>tr>td {
        background-color: #1A2E52;
        border-color: #4F98F6;
        color: white;
    }

    .cfbgc-table-bordered .cfbgc-table-header>table {
        border-color: #4F98F6;
    }

    .cfbgc-table-bordered .cfbgc-table-body>table {
        border-color: #4F98F6;
    }

    .cfbgc-table-tbody>tr:hover:not(.cfbgc-table-expanded-row):not(.cfbgc-table-row-selected)>td {
        // background-color: #606b78;
        background: #4EA0FC;
        color: #8FD4FF;
    }

    .cfbgc-table-small {
        border-color: #4F98F6;
    }

    .cfbgc-table-small.cfbgc-table-bordered .cfbgc-table-content {
        border-color: #4F98F6;
    }

    .cfbgc-table-thead>tr>th.cfbgc-table-column-has-actions.cfbgc-table-column-has-sorters:hover {
        background-color: #606b78;
    }

    // 弹窗
    .cfbgc-modal {
        border-radius: 0;
        background-color: transparent;
        background-image: url('@/assets/images/框4.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .cfbgc-modal-header {
        background-color: transparent;
    }

    .cfbgc-modal-title {
        color: #FEB64E;
        display: flex;
        justify-content: center;
        background-image: url('@/assets/images/框5.png');
        background-size: auto 70%;
        background-repeat: no-repeat;
        background-position-x: center;
        background-position-y: 90%;
        padding: 15px 0;
    }

    .cfbgc-modal-title::before {
        display: none;
    }

    .cfbgc-modal-close-x {
        color: white;
    }

    .cfbgc-modal-content {
        background-color: transparent;
        // background-image: url('@/assets/images/框4.png');
        // background-size: 100% 100%;
        // background-repeat: no-repeat;
    }

    // popover
    .cfbgc-popover-inner {
        background: #093469;
    }

    .cfbgc-popover-placement-top>.cfbgc-popover-content>.cfbgc-popover-arrow,
    .cfbgc-popover-placement-topLeft>.cfbgc-popover-content>.cfbgc-popover-arrow,
    .cfbgc-popover-placement-topRight>.cfbgc-popover-content>.cfbgc-popover-arrow {
        border-top-color: transparent;
        border-right-color: #093469;
        border-bottom-color: #093469;
    }


    // 子应用样式
    .childContent {
        padding: 0 20px 20px 20px;
        background-color: #0c1738;
        background-image: url('@/assets/images/框1.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .leftBox {
            padding: 10px;
            padding-top: 30px;
            background-image: url('@/assets/images/框2.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }

    .formItem {
        .label {
            color: white;
        }
    }

    .filterContent {
        padding: 5px;
        background-image: url('@/assets/images/框3.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .filterTitle {
            color: #FEB64E;
        }

        .filterItem .label {
            color: white;
        }
    }

    .blockItem {
        border-radius: 0;
        background-color: transparent;
        background-image: url('@/assets/images/框4.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .blockTitle {
            color: #FEB64E;
            padding: 5px;
            background-image: url('@/assets/images/框3.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;

            .shortLine {
                display: none;
            }
        }
    }
}