<template>
  <div class="useChemicals">
    <div>
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      ></OgwSearch>
    </div>
    <div>
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        :show-index="true"
        :loading="loading"
        @cell-click="handleCellClick"
      ></OgwTable>
    </div>
    <DialogBox
      :title="'深海一号甲醇采购2025年1月-2025-5月'"
      :isShow.sync="showInfo"
    >
      <OgwTable
        :columns="dColumns"
        :data="dTableData"
        :showActions="false"
        :show-index="true"
      ></OgwTable>
    </DialogBox>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import { getUseChemical } from "@/api/ogwActiveRecord/useChemical.js";
import { getProd } from "@/api/common.js";
export default {
  name: "useChemicals",
  components: {
    OgwSearch,
    OgwTable,
  },
  created() {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth() 返回 0-11，需要 +1
    const dateArr = [];
    dateArr[0] = currentYear + "-" + String(1).padStart(2, "0"); // 年初：01月
    dateArr[1] = currentYear + "-" + String(currentMonth).padStart(2, "0"); // 当前月
    this.searchForm.monthRange = dateArr;
  },
  async mounted() {
    await this.getProdList();
    this.searchForm.orgId = this.orgList[0]?.orgId;
    console.log('this.searchForm.orgId', this.searchForm.orgId);
    
    this.getTableInfo();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ];
    },
  },
  data() {
    return {
      loading: false,
      orgList: [],
      deviceOptions: [],
      searchForm: {
        orgId: "",
        deviceNameCode: "",
        monthRange: [],
      },
      columns: [
        { label: "平台名称", prop: "productionName" },
        { label: "化学药剂类型/代号", prop: "chemicalName" },
        { label: "(加注量+库存量)/入库量", prop: "radio", clickable: true },
        { label: "入库量(L)", prop: "purchaseQuantity" },
        { label: "累计加注量(L)", prop: "injectionVolume" },
        { label: "库存量(L)", prop: "inventory" },
        { label: "采购次数", prop: "purchaseNum", clickable: true },
        { label: "最近一次采购时间/采购量(L)", prop: "lastPurchaseInfo" },
      ],
      tableData: [],
      dColumns: [
        {
          label: "采购时间",
          prop: "purchaseDate",
          formatter: (row) => {
            // 2025-07-02 00:00:00
            const buyTime = row.purchaseDate.split(" ")[0];
            return buyTime.replace(
              /(\d{4})-(\d+)-(\d+)/,
              (match, year, month, day) =>
                `${year}年${parseInt(month)}月${parseInt(day)}日`
            );
          },
        },
        { label: "采购量(L)", prop: "quantity" },
        { label: "采购单价(元/L)", prop: "price" },
      ],
      dTableData: [],
      showInfo: false,
    };
  },
  methods: {
    handleCellClick({ row, prop, index, value }) {
      // console.log(`点击了第 ${index + 1} 行的 ${prop} 列，值为：`, row);
      switch (prop) {
        case "radio":
          this.$router.push({
            name: "radio",
          });
          break;
        case "purchaseNum":
          this.showInfo = true;
          this.dTableData = row?.purchaseList;
          break;
      }
    },
    handleSearch(value) {
      this.searchForm.orgId = value?.orgId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.monthRange = value.monthRange;
      this.getTableInfo();
    },
    handleReset(value) {
      // 重置后重新设置默认的月份范围
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      const dateArr = [];
      dateArr[0] = currentYear + "-" + String(1).padStart(2, "0");
      dateArr[1] = currentYear + "-" + String(currentMonth).padStart(2, "0");

      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
        monthRange: dateArr,
      };
      this.getTableInfo();
    },
    async getTableInfo() {
      this.loading = true;
      try {
        const data = {
          orgId: this.searchForm.orgId || null,
          hzNo: this.searchForm.deviceNameCode || null,
          startTime: this.searchForm.monthRange[0],
          endTime: this.searchForm.monthRange[1],
        };
        const res = await getUseChemical(data);
        if (res.code === 200) {
          this.tableData = res.data;
          this.tableData.forEach((item) => {
            item.buyTimeAndBuyQuantity = this.formatDateAmount(
              item.buyTimeAndBuyQuantity
            );
          });
        }
      } catch (error) {
        console.log("数据获取失败", error);
      } finally {
        this.loading = false;
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.searchForm.orgId = this.orgList[0]?.orgId;
      }
    },
    /**
     * 日期转换
     * @param inputStr
     */
    formatDateAmount(inputStr) {
      if (inputStr == null || inputStr == "") {
        return inputStr;
      }
      return inputStr.replace(
        /(\d{4})-0?(\d+)-0?(\d+)\/(\d+)(?:\.\d+)?/,
        (match, year, month, day, amount) =>
          `${year}年${parseInt(month)}月${parseInt(day)}日/${amount}L`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
[data-theme="dark"] .useChemicals {
  background: #162549;
}

[data-theme="tint"] .useChemicals {
  background: #fff;
}
</style>
