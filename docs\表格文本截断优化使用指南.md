# 表格文本截断优化使用指南

## 📋 概述

本文档介绍了 OgwColumn 组件中新增的文本截断和 tooltip 功能，该功能可以优雅地处理表格单元格中文本过长的显示问题。

## ✨ 功能特性

### 🎯 核心功能
- **智能文本截断**: 根据列宽度自动截断过长文本并显示省略号
- **悬停 Tooltip**: 鼠标悬停时显示完整内容
- **主题适配**: 支持浅色和深色主题的 tooltip 样式
- **性能优化**: 智能判断是否需要显示 tooltip，避免不必要的渲染
- **可配置性**: 支持自定义 tooltip 行为和样式

### 🔧 技术特点
- **布局稳定**: 文本截断不会影响表格布局
- **响应式**: 根据列宽度动态计算最大文本长度
- **兼容性**: 与现有的可编辑、可点击等功能完全兼容
- **可扩展**: 支持自定义 tooltip 内容格式化

## 🚀 基本使用

### 默认行为
组件会自动为所有单元格启用智能文本截断和 tooltip 功能：

```vue
<template>
  <OgwTable :columns="columns" :data="tableData" />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { label: "产品名称", prop: "productName", width: 150 },
        { label: "详细描述", prop: "description", width: 200 },
        { label: "备注信息", prop: "remarks", minWidth: 120 }
      ],
      tableData: [
        {
          productName: "深海一号天然气开采设备",
          description: "这是一个非常详细的产品描述，包含了产品的各种技术参数和使用说明",
          remarks: "重要备注信息"
        }
      ]
    };
  }
};
</script>
```

## ⚙️ 高级配置

### 1. 控制 Tooltip 显示

```javascript
const columns = [
  {
    label: "产品名称",
    prop: "productName",
    width: 150,
    showTooltip: true  // 强制显示 tooltip
  },
  {
    label: "状态",
    prop: "status",
    width: 80,
    showTooltip: false  // 禁用 tooltip
  },
  {
    label: "描述",
    prop: "description",
    width: 200
    // 不设置 showTooltip，使用智能判断
  }
];
```

### 2. 自定义最大文本长度

```javascript
const columns = [
  {
    label: "产品名称",
    prop: "productName",
    width: 200,
    maxTextLength: 15  // 最多显示15个字符
  }
];
```

### 3. 自定义 Tooltip 内容

```javascript
const columns = [
  {
    label: "产品信息",
    prop: "productInfo",
    width: 150,
    tooltipFormatter: (row, value) => {
      return `产品: ${row.productName}\n描述: ${value}\n创建时间: ${row.createTime}`;
    }
  }
];
```

## 🎨 样式定制

### 自定义 Tooltip 样式

如果需要进一步定制 tooltip 样式，可以通过 CSS 覆盖：

```scss
// 自定义浅色主题 tooltip
::v-deep .ogw-table-tooltip-default .el-tooltip__popper {
  background-color: #f8f9fa !important;
  color: #333333 !important;
  border: 1px solid #dee2e6 !important;
  font-size: 13px !important;
}

// 自定义深色主题 tooltip
::v-deep .ogw-table-tooltip-dark .el-tooltip__popper {
  background-color: #2c3e50 !important;
  color: #ecf0f1 !important;
  border: 1px solid #34495e !important;
}
```

## 📊 实际应用示例

### 生产计划表格示例

```vue
<template>
  <div class="production-plan">
    <OgwTable
      :columns="columns"
      :data="tableData"
      :show-index="true"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          label: "设备名称",
          prop: "deviceName",
          width: 150,
          showTooltip: true
        },
        {
          label: "生产计划",
          prop: "productionPlan",
          width: 200,
          maxTextLength: 20,
          tooltipFormatter: (row, value) => {
            return `计划详情: ${value}\n负责人: ${row.manager}\n截止日期: ${row.deadline}`;
          }
        },
        {
          label: "状态",
          prop: "status",
          width: 80,
          showTooltip: false,
          formatter: (row, value) => {
            const statusMap = {
              'running': '运行中',
              'stopped': '已停止',
              'maintenance': '维护中'
            };
            return statusMap[value] || value;
          }
        },
        {
          label: "备注",
          prop: "remarks",
          minWidth: 120,
          editable: true
        }
      ],
      tableData: [
        {
          deviceName: "深海一号天然气开采平台主控设备",
          productionPlan: "2024年第一季度天然气开采计划，预计产量1000万立方米，包含设备维护和人员培训",
          status: "running",
          manager: "张工程师",
          deadline: "2024-03-31",
          remarks: "设备运行正常，需要定期检查压力表和温度传感器"
        }
      ]
    };
  }
};
</script>
```

## 🔍 最佳实践

### 1. 列宽度设置建议
- **固定宽度列**: 适合状态、操作等内容相对固定的列
- **最小宽度列**: 适合文本内容长度变化较大的列
- **合理的宽度分配**: 确保重要信息有足够的显示空间

### 2. Tooltip 使用建议
- **重要信息列**: 建议强制启用 tooltip（`showTooltip: true`）
- **简短内容列**: 可以禁用 tooltip（`showTooltip: false`）
- **自定义格式**: 对于复杂数据，使用 `tooltipFormatter` 提供更丰富的信息

### 3. 性能优化建议
- **避免过度使用**: 不是所有列都需要 tooltip
- **合理设置延迟**: 默认 500ms 延迟可以避免误触发
- **控制 tooltip 内容长度**: 避免 tooltip 内容过长影响用户体验

## 🐛 常见问题

### Q: Tooltip 不显示怎么办？
A: 检查以下几点：
1. 确保文本长度超过了计算的最大长度
2. 检查是否设置了 `showTooltip: false`
3. 确认数据不为空值

### Q: 如何调整 Tooltip 显示时机？
A: 可以通过修改 `open-delay` 属性来调整延迟时间，或者通过 `maxTextLength` 来调整触发条件。

### Q: 在编辑状态下 Tooltip 会影响编辑吗？
A: 不会，编辑状态下 tooltip 会自动禁用，不会干扰用户编辑操作。

## 📈 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 新增智能文本截断功能
- ✅ 新增悬停 tooltip 显示
- ✅ 支持主题适配
- ✅ 支持自定义配置
- ✅ 完善的样式和交互体验

---

**让表格文本显示更优雅，用户体验更友好！** 🎉
