<!-- 表单表格 -->
<template>
  <div class="ogw-table">
    <div class="table-container">
      <el-table
        :key="tableKey"
        :data="tableWithSummaries"
        border
        style="min-width: 1000px; width: 100%"
        :span-method="handleSpan"
        v-bind="$attrs"
        :row-class-name="getRowClassName"
        v-loading="loading"
        element-loading-text="数据加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <el-table-column
          v-if="showIndex"
          label="序号"
          width="60"
          align="center"
        >
          <template v-slot="scope">
            <span v-if="!scope.row._isSummaryRow">{{
              computeIndex(scope.$index)
            }}</span>
          </template>
        </el-table-column>

        <!-- 使用子组件渲染列 -->
        <OgwColumn
          v-for="col in columns"
          :key="col.prop || col.label"
          :column="col"
          :show-index="showIndex"
          :summary-config="summaryConfig"
          :editing-cell="editingCell"
          @enter-edit="enterEdit"
          @exit-edit="exitEdit"
          @cell-change="onCellChange"
          @cell-click="handleCellClick"
        >
          <!-- 透传所有插槽 -->
          <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
            <slot :name="slotName" v-bind="scope" />
          </template>
        </OgwColumn>

        <el-table-column
          v-if="showActions"
          label="操作"
          fixed="right"
          :width="actionColumnWidth"
          align="center"
        >
          <template v-slot="scope">
            <template v-if="!scope.row._isSummaryRow">
              <slot name="actions" v-bind="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="$emit('edit', scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="mini"
                  @click="$emit('delete', scope.row)"
                  >删除</el-button
                >
              </slot>
            </template>
          </template>
        </el-table-column>

        <!-- 空数据提示 -->
        <template #empty>
          <div class="empty-data">
            <el-empty description="暂无数据" :image-size="80">
              <template #description>
                <span class="empty-description">暂无数据</span>
              </template>
            </el-empty>
          </div>
        </template>
      </el-table>
    </div>

    <div class="table-pagination-wrapper" v-if="pagination">
      <el-pagination
        class="table-pagination"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="internalCurrentPage"
        :page-size="internalPageSize"
        :total="total"
        :page-sizes="pageSizes"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import OgwColumn from "./OgwColumn.vue";

export default {
  name: "SmartTable",
  components: {
    OgwColumn,
  },
  props: {
    data: Array,
    columns: {
      type: Array,
      default: () => [],
      validator: (value) => {
        return value.every((col) => {
          const hasChildren = col.children && col.children.length;
          return hasChildren ? true : !!col.prop;
        });
      },
    },
    mergeKeys: {
      type: Array,
      default: () => [],
    },
    showActions: Boolean,
    actionColumnWidth: [Number, String],
    showIndex: Boolean,
    pagination: {
      type: Boolean,
      default: false,
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100],
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    total: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    summaryConfig: {
      type: Object,
      default: () => ({
        sumColumns: [],
        groupField: "",
        showSubTotal: false,
        showGrandTotal: false,
        showCustomSubTotal: false,
        showCustomGrandTotal: false,
        subTotalText: "小计",
        grandTotalText: "合计",
        customSubTotalText: "自定义小计",
        customGrandTotalText: "自定义总计",
        subTotalTextMergeColumns: [],
        grandTotalTextMergeColumns: [],
        customSubTotalTextMergeColumns: [],
        customGrandTotalTextMergeColumns: [],
        summaryRowClass: "summary-row",
        customGrandTotalData: null, // 自定义总计数据函数或对象
        customSubTotalData: null, // 自定义小计数据函数
      }),
    },
  },
  data() {
    return {
      tableKey: Date.now(),
      mergeMaps: {},
      editingCell: { rowIndex: null, prop: null },
      internalCurrentPage: this.currentPage,
      internalPageSize: this.pageSize,
    };
  },
  mounted() {
    // 添加全局点击事件监听，用于退出编辑模式
    document.addEventListener("click", this.handleDocumentClick);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener("click", this.handleDocumentClick);
  },
  computed: {
    tableWithSummaries() {
      // 如果没有数据或数据不是数组，返回空数组
      if (!Array.isArray(this.data)) {
        return [];
      }

      const data = [...this.data];
      const result = [];
      let group = [];
      let lastValue = null;

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const curValue = row[this.summaryConfig.groupField];

        if (lastValue !== null && curValue !== lastValue) {
          // 处理上一组数据
          result.push(...group);

          // 添加分组小计
          if (group.length > 1 && this.summaryConfig.showSubTotal) {
            result.push(
              this.createSummaryRow(
                group,
                this.summaryConfig.subTotalText,
                "sub"
              )
            );
          }

          // 为合并组或单行数据添加自定义小计行
          if (this.summaryConfig.showCustomSubTotal) {
            if (group.length > 1) {
              // 合并组：只添加一条自定义小计行
              let customData = group[0].customData || {};

              // 如果配置了 customSubTotalData 函数，则调用它
              if (typeof this.summaryConfig.customSubTotalData === "function") {
                customData =
                  this.summaryConfig.customSubTotalData(group, group[0]) ||
                  customData;
              }

              result.push(
                this.createCustomSummaryRow(
                  this.summaryConfig.customSubTotalText,
                  "customSub",
                  customData,
                  group[0] // 使用组内第一行作为参考
                )
              );
            } else {
              // 单行数据：为每行添加自定义小计行
              group.forEach((groupRow) => {
                let customData = groupRow.customData || {};

                // 如果配置了 customSubTotalData 函数，则调用它
                if (
                  typeof this.summaryConfig.customSubTotalData === "function"
                ) {
                  customData =
                    this.summaryConfig.customSubTotalData(
                      [groupRow],
                      groupRow
                    ) || customData;
                }

                result.push(
                  this.createCustomSummaryRow(
                    this.summaryConfig.customSubTotalText,
                    "customSub",
                    customData,
                    groupRow
                  )
                );
              });
            }
          }

          group = [];
        }
        group.push(row);
        lastValue = curValue;
      }

      // 处理最后一组
      result.push(...group);

      // 添加分组小计
      if (group.length > 1 && this.summaryConfig.showSubTotal) {
        result.push(
          this.createSummaryRow(group, this.summaryConfig.subTotalText, "sub")
        );
      }

      // 为最后一组添加自定义小计行
      if (this.summaryConfig.showCustomSubTotal) {
        if (group.length > 1) {
          // 合并组：只添加一条自定义小计行
          let customData = group[0].customData || {};

          // 如果配置了 customSubTotalData 函数，则调用它
          if (typeof this.summaryConfig.customSubTotalData === "function") {
            customData =
              this.summaryConfig.customSubTotalData(group, group[0]) ||
              customData;
          }

          result.push(
            this.createCustomSummaryRow(
              this.summaryConfig.customSubTotalText,
              "customSub",
              customData,
              group[0]
            )
          );
        } else {
          // 单行数据：为每行添加自定义小计行
          group.forEach((groupRow) => {
            let customData = groupRow.customData || {};

            // 如果配置了 customSubTotalData 函数，则调用它
            if (typeof this.summaryConfig.customSubTotalData === "function") {
              customData =
                this.summaryConfig.customSubTotalData([groupRow], groupRow) ||
                customData;
            }

            result.push(
              this.createCustomSummaryRow(
                this.summaryConfig.customSubTotalText,
                "customSub",
                customData,
                groupRow
              )
            );
          });
        }
      }

      // 添加总计
      if (this.summaryConfig.showGrandTotal && data.length > 0) {
        result.push(
          this.createSummaryRow(
            data,
            this.summaryConfig.grandTotalText,
            "grand"
          )
        );
      }

      // 添加自定义总计
      if (this.summaryConfig.showCustomGrandTotal && data.length > 0) {
        let customGrandTotalData = {};

        // 如果配置了 customGrandTotalData 函数，则调用它
        if (typeof this.summaryConfig.customGrandTotalData === "function") {
          customGrandTotalData =
            this.summaryConfig.customGrandTotalData(data) || {};
        } else if (this.summaryConfig.customGrandTotalData) {
          // 如果是静态数据对象，直接使用
          customGrandTotalData = this.summaryConfig.customGrandTotalData;
        }

        result.push(
          this.createCustomSummaryRow(
            this.summaryConfig.customGrandTotalText,
            "customGrand",
            customGrandTotalData
          )
        );
      }

      return result;
    },
  },
  watch: {
    data: {
      handler() {
        this.calculateMerge();
      },
      immediate: true,
    },
    columns: {
      handler() {
        this.tableKey = Date.now();
      },
    },
  },
  methods: {
    createCustomSummaryRow(
      text,
      type = "customSub",
      customData = {},
      originalRow = null
    ) {
      const row = {
        _isSummaryRow: true,
        _summaryText: text,
        _summaryType: type,
        _originalRowData: originalRow, // 保存原始行数据引用
        ...customData,
      };

      // 如果有原始行数据，继承合并相关的字段值
      if (originalRow && this.mergeKeys.length > 0) {
        this.mergeKeys.forEach((key) => {
          if (!(key in customData)) {
            row[key] = originalRow[key];
          }
        });
      }

      // 对于自定义总计行，不需要继承原始行数据的字段值
      if (type === "customGrand") {
        this.summaryConfig.sumColumns.forEach((col) => {
          if (!(col in customData)) {
            row[col] = null;
          }
        });
      } else {
        this.summaryConfig.sumColumns.forEach((col) => {
          if (!(col in customData)) {
            row[col] = null;
          }
        });
      }

      return row;
    },
    createSummaryRow(rows, text, type = "sub") {
      const row = {
        _isSummaryRow: true,
        _summaryText: text,
        _summaryType: type,
      };
      // 总计
      this.summaryConfig.sumColumns.forEach((col) => {
        const sum = rows.reduce((sum, r) => sum + (parseFloat(r[col]) || 0), 0);
        row[col] = Number(sum.toFixed(2));
      });
      return row;
    },
    calculateMerge() {
      if (this.mergeKeys.length === 0) return;

      const data = this.tableWithSummaries;

      this.mergeKeys.forEach((key) => {
        const map = [];
        let span = 1;
        let lastValue = null;
        let currentSpanStart = 0;

        for (let i = 0; i < data.length; i++) {
          const row = data[i];

          if (row._isSummaryRow) {
            if (row._summaryType === "customSub" && row._originalRowData) {
              // 自定义小计行：检查是否应该与前面的数据行合并
              const originalValue = row._originalRowData[key];

              // 查找前面最近的非汇总行
              let prevDataRowIndex = i - 1;
              while (
                prevDataRowIndex >= 0 &&
                data[prevDataRowIndex]._isSummaryRow
              ) {
                prevDataRowIndex--;
              }

              if (
                prevDataRowIndex >= 0 &&
                data[prevDataRowIndex][key] === originalValue
              ) {
                // 与前面的数据行合并
                map[currentSpanStart] += 1;
                map.push(0);
              } else {
                // 不合并，独立显示
                map.push(1);
                currentSpanStart = i;
              }
            } else if (row._summaryType === "sub") {
              // 分组小计行：与前面的数据行合并
              if (currentSpanStart < i) {
                map[currentSpanStart] += 1;
              }
              map.push(0);
            } else {
              // 其他汇总行（总计、自定义总计）：不合并
              map.push(0);
            }
            continue;
          }

          const curValue = row[key];

          if (curValue !== lastValue) {
            span = 1;
            currentSpanStart = i;
            map.push(1);
          } else {
            span++;
            map[currentSpanStart] = span;
            map.push(0);
          }

          lastValue = curValue;
        }

        this.$set(this.mergeMaps, key, map);
      });
    },
    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (this.mergeKeys.length === 0) return { rowspan: 1, colspan: 1 };

      // 获取当前列的实际prop
      let colProp = null;
      let currentCol = null;

      const findColumn = (columns, index) => {
        let idx = index;
        for (const col of columns) {
          if (col.children && col.children.length) {
            if (idx < col.children.length) {
              return col.children[idx];
            }
            idx -= col.children.length;
          } else {
            if (idx === 0) {
              return col;
            }
            idx--;
          }
        }
        return null;
      };

      currentCol = findColumn(
        this.columns,
        columnIndex - (this.showIndex ? 1 : 0)
      );
      colProp = currentCol?.prop;

      if (row._isSummaryRow) {
        const type = row._summaryType;
        let mergeCols = [];

        if (type === "grand") {
          mergeCols = this.summaryConfig.grandTotalTextMergeColumns;
        } else if (type === "sub") {
          mergeCols = this.summaryConfig.subTotalTextMergeColumns;
        } else if (type === "customSub") {
          mergeCols = this.summaryConfig.customSubTotalTextMergeColumns;
        } else if (type === "customGrand") {
          mergeCols = this.summaryConfig.customGrandTotalTextMergeColumns;
        }

        if (mergeCols.includes(colProp)) {
          if (mergeCols[0] === colProp) {
            return { rowspan: 1, colspan: mergeCols.length };
          } else {
            return { rowspan: 0, colspan: 0 };
          }
        }
      }

      // 检查当前列是否是需要合并的列
      if (colProp && this.mergeKeys.includes(colProp)) {
        const mergeMap = this.mergeMaps[colProp];
        if (mergeMap && mergeMap[rowIndex] !== undefined) {
          return { rowspan: mergeMap[rowIndex], colspan: 1 };
        }
      }

      return { rowspan: 1, colspan: 1 };
    },
    handleSizeChange(newSize) {
      this.internalPageSize = newSize;
      this.internalCurrentPage = 1;
      this.$emit("page-change", {
        pageSize: newSize,
        currentPage: 1,
      });
    },
    handleCurrentChange(newPage) {
      this.internalCurrentPage = newPage;
      this.$emit("page-change", {
        pageSize: this.internalPageSize,
        currentPage: newPage,
      });
    },
    getRowClassName({ row }) {
      return row._isSummaryRow ? this.summaryConfig.summaryRowClass : "";
    },
    handleDocumentClick(event) {
      // 检查点击是否在表格内部
      const tableElement = this.$el.querySelector(".el-table");
      if (tableElement && !tableElement.contains(event.target)) {
        // 点击在表格外部，退出编辑模式
        this.exitEdit();
      }
    },
    enterEdit({ index, prop }) {
      this.editingCell = { rowIndex: index, prop };
      // 向外部发出进入编辑模式事件
      this.$emit("enter-edit", { index, prop });
      // 下一帧聚焦到输入框
      this.$nextTick(() => {
        this.focusEditingInput();
      });
    },
    // 聚焦编辑输入框
    focusEditingInput() {
      // 等待DOM更新完成
      setTimeout(() => {
        const editingContainer = this.$el.querySelector(".editing-input");
        if (!editingContainer) return;

        // 尝试不同类型的输入控件
        const selectors = [
          ".el-input__inner",
          ".el-textarea__inner",
          ".el-select input",
          ".el-date-editor input",
          "input",
          "textarea",
        ];

        let focusedInput = null;

        for (const selector of selectors) {
          const input = editingContainer.querySelector(selector);
          if (input && !input.disabled && !input.readOnly) {
            focusedInput = input;
            break;
          }
        }

        if (focusedInput) {
          focusedInput.focus();

          // 对于文本输入框，选中所有内容
          if (
            focusedInput.type === "text" ||
            focusedInput.type === "number" ||
            focusedInput.tagName === "TEXTAREA"
          ) {
            // 延迟选择，确保焦点已设置
            setTimeout(() => {
              if (focusedInput.select) {
                focusedInput.select();
              } else if (focusedInput.setSelectionRange) {
                focusedInput.setSelectionRange(0, focusedInput.value.length);
              }
            }, 10);
          }

          // 对于下拉选择器，自动展开
          if (focusedInput.closest(".el-select")) {
            const selectComponent = focusedInput.closest(".el-select");
            if (selectComponent && selectComponent.click) {
              setTimeout(() => selectComponent.click(), 50);
            }
          }
        }
      }, 50);
    },
    exitEdit() {
      this.editingCell = { rowIndex: null, prop: null };
    },
    onCellChange({ row, prop, index, value }) {
      row[prop] = value;
      this.$emit("cell-change", { row, prop, index, value });
      // 值改变后退出编辑模式
      this.exitEdit();
    },
    emitCellClick(row, prop, index) {
      this.$emit("cell-click", { row, prop, index, value: row[prop] });
    },
    handleCellClick({ event, row, prop, index }) {
      // 阻止事件冒泡，避免触发 document 点击事件
      event.stopPropagation();

      // 如果点击的不是当前目标元素，不处理
      if (event.target !== event.currentTarget) return;

      // 发出单元格点击事件
      this.emitCellClick(row, prop, index);
    },
    computeIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },
  },
};
</script>

<style lang="scss" scoped>
.ogw-table {
  height: 100%;
  position: relative;

  .table-container {
    position: relative;
  }

  .empty-data {
    padding: 40px 0;

    .empty-description {
      color: #909399;
      font-size: 14px;
    }
  }
}

// 浅色主题单元格样式
[data-theme="default"],
[data-theme="tint"] {
  .summary-cell {
    font-weight: bold;
    color: #606266;
  }

  .clickable-cell {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
  }

  .editable-cell {
    cursor: pointer;
    color: #409eff;
    border-bottom: 1px dashed #e2e2e2;
  }

  .table-pagination-wrapper {
    padding: 12px 20px;
    text-align: right;
    background-color: #fff;
  }

  .empty-data {
    .empty-description {
      color: #909399;
    }
  }
}

// 深色主题单元格样式
[data-theme="dark"] {
  .summary-cell {
    font-weight: bold;
    color: #cce4ff;
  }

  .clickable-cell {
    color: #4ea0fc;
    cursor: pointer;
    text-decoration: underline;
  }

  .editable-cell {
    cursor: pointer;
    color: #4ea0fc;
    border-bottom: 1px dashed #1783ff;
  }

  .table-pagination-wrapper {
    padding: 12px 20px;
    text-align: right;
    background-color: #1b2242;
  }

  .empty-data {
    .empty-description {
      color: #cce4ff;
    }
  }
}

// 加载动画样式优化
::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

::v-deep .el-loading-text {
  color: #fff !important;
  font-size: 14px !important;
}

::v-deep .el-loading-spinner {
  .el-icon-loading {
    font-size: 28px !important;
    color: #409eff !important;
  }
}
// 操作列顶部边框
[data-theme="dark"] {
  .ogw-table {
    ::v-deep .el-table__fixed-right::before {
      top: 0;
      background-color: #1783ff !important;
    }
  }
}

[data-theme="tint"] {
  .ogw-table {
    ::v-deep .el-table__fixed-right::before {
      top: 0;
      background-color: #e2e2e2 !important;
    }
  }
}

// 浅色主题样式
[data-theme="default"],
[data-theme="tint"] {
  .ogw-table {
    ::v-deep .el-table,
    .el-table__expanded-cell {
      background-color: #fff;
    }

    ::v-deep .el-table tr {
      background-color: #fff !important;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      color: #000 !important;
    }

    ::v-deep .el-table__header-wrapper {
      background: #fff;
      border-width: 1px 1px 0 0px;
      border-style: solid;
      border-color: #e2e2e2;

      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

// 深色主题样式
[data-theme="dark"] {
  .ogw-table {
    ::v-deep .el-table,
    .el-table__expanded-cell {
      background-color: #1b2242;
    }

    ::v-deep .el-table tr {
      background-color: #1b2242 !important;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
      color: #ffffff !important;
    }

    ::v-deep .el-table__header-wrapper {
      background: #162549;
      border-width: 1px 1px 0 0px;
      border-style: solid;
      border-color: #1783ff;

      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

::v-deep .el-table::before {
  height: 0;
}

::v-deep .el-table--border::after {
  width: 0;
}

// 浅色主题边框和交互样式
[data-theme="default"],
[data-theme="tint"] {
  .ogw-table {
    ::v-deep .el-table--border::before {
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: #e2e2e2 !important;
    }

    ::v-deep .el-table--border {
      .el-table__header-wrapper {
        .el-table__cell {
          background: #fafafa;
        }
      }
    }

    ::v-deep .el-select-dropdown {
      background-color: #fff !important;
    }

    // 修改边框颜色
    ::v-deep .el-table th.el-table__cell.is-leaf {
      background: #eff4f8 !important;
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-table td.el-table__cell {
      border-color: #e2e2e2 !important;
    }

    ::v-deep .el-input__inner {
      background-color: #fff !important;
      color: #000 !important;
    }

    ::v-deep .el-table__row:hover > td {
      background: #cce8ff !important;
      color: rgba(0, 0, 0, 0.85);
    }

    // 斑马纹效果 - 浅色主题
    ::v-deep .el-table__body tr:nth-child(even) td {
      background-color: #fafafa !important;
    }

    ::v-deep .el-table__body tr:nth-child(odd) td {
      background-color: #fff !important;
    }
  }
}

// 深色主题边框和交互样式
[data-theme="dark"] {
  .ogw-table {
    ::v-deep .el-table--border::before {
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: #1783ff !important;
    }

    ::v-deep .el-table--border {
      .el-table__header-wrapper {
        .el-table__cell {
          background: #162549;
        }
      }
    }

    ::v-deep .el-select-dropdown {
      background-color: #1a2e52 !important;
    }

    // 修改边框颜色
    ::v-deep .el-table th.el-table__cell.is-leaf {
      background: #162549 !important;
      border-color: #1783ff !important;
      color: #ffffff !important;
    }

    ::v-deep .el-table td.el-table__cell {
      border-color: #1783ff !important;
    }

    ::v-deep .el-input__inner {
      background-color: #1a2e52 !important;
      color: #ffffff !important;
      border-color: #4f98f6 !important;
    }

    ::v-deep .el-table__row:hover > td {
      background: #254489 !important;
      color: #ffffff;
    }

    // 斑马纹效果 - 深色主题
    ::v-deep .el-table__body tr:nth-child(even) td {
      background-color: #1a2e52 !important;
    }

    ::v-deep .el-table__body tr:nth-child(odd) td {
      background-color: #1b2242 !important;
    }
  }
}

// 浅色主题日期选择器样式
[data-theme="default"],
[data-theme="tint"] {
  .ogw-table {
    ::v-deep .table-date-picker {
      z-index: 3000 !important;
    }

    ::v-deep .editing-input {
      .el-date-editor {
        width: 100% !important;

        .el-input__inner {
          background-color: #fff !important;
          border: 1px solid #409eff !important;
          cursor: pointer;
          color: #000 !important;
        }

        .el-input__prefix {
          cursor: pointer;
        }
      }
    }

    ::v-deep .el-date-picker {
      z-index: 3000 !important;
    }
  }
}

// 深色主题日期选择器样式
[data-theme="dark"] {
  .ogw-table {
    ::v-deep .table-date-picker {
      z-index: 3000 !important;
    }

    ::v-deep .editing-input {
      .el-date-editor {
        width: 100% !important;

        .el-input__inner {
          background-color: #1a2e52 !important;
          border: 1px solid #4f98f6 !important;
          cursor: pointer;
          color: #ffffff !important;
        }

        .el-input__prefix {
          cursor: pointer;
        }
      }
    }

    ::v-deep .el-date-picker {
      z-index: 3000 !important;
      background-color: #1a2e52 !important;
      border-color: #4f98f6 !important;
    }
  }
}

// 浅色主题空数据状态样式
[data-theme="default"],
[data-theme="tint"] {
  .ogw-table {
    ::v-deep .el-table__empty-block {
      background-color: #fff;
    }

    ::v-deep .el-empty {
      .el-empty__image {
        width: 80px;
        height: 80px;
      }

      .el-empty__description {
        margin-top: 16px;

        .empty-description {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}

// 深色主题空数据状态样式
[data-theme="dark"] {
  .ogw-table {
    ::v-deep .el-table__empty-block {
      background-color: #1b2242;
    }

    ::v-deep .el-empty {
      .el-empty__image {
        width: 80px;
        height: 80px;
      }

      .el-empty__description {
        margin-top: 16px;

        .empty-description {
          color: #cce4ff;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
