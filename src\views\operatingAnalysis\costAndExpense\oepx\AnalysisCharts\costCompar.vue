<template>
  <div class="costCompar">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "costCompar",
  data() {
    return {
      xData: ["YC13-1", "LS17-2", "LS25-1", "YC13-10", "WC16-2"],
      y1Data: [40, 38, 40, 70, 25],
      y2Data: [26, 28, 26, 52, 27],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {
        color: ["#248EFF", "#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12,
          },
          formatter: (params) => {
            // 获取X轴标签作为标题
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;

            // 过滤重复的数据系列，只保留每个唯一名称的第一个系列
            const uniqueSeries = [];
            const seenNames = new Set();

            params.forEach((param) => {
              if (!seenNames.has(param.seriesName)) {
                seenNames.add(param.seriesName);
                uniqueSeries.push(param);
              }
            });

            // 计算同比变化百分比
            const currentValue = this.y1Data[params[0].dataIndex];
            const lastYearValue = this.y2Data[params[0].dataIndex];
            const changePercent = ((currentValue - lastYearValue) / lastYearValue * 100).toFixed(2);

            // 遍历过滤后的唯一数据系列
            uniqueSeries.forEach((param) => {
              const unit = param.seriesName === "同比变化" ? "%" : "万元";
              let displayValue = param.value;

              // 如果是同比变化，显示百分比格式
              if (param.seriesName === "同比变化") {
                displayValue = changePercent;
              } else {
                // 其他数值保留两位小数
                displayValue = Number(param.value).toFixed(2);
              }

              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${displayValue}${unit}
              </div>`;
            });

            return result;
          },
        },
        legend: {
          data: ["OPEX费用", "去年同期OPEX","同比变化"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "万元",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
          {
            type: "value",
            name: "%",
            position: "right",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "left",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}%",
            },
            splitLine: {
              show: false, // 不显示右侧Y轴的分割线，避免重复
            },
          },
        ],
        series: [
          {
            name: "同比变化",
            type: "line",
            yAxisIndex: 1, // 使用右侧Y轴
            symbol: "circle", // 圆形数据点，与参考图片一致
            symbolSize: 7, // 数据点大小，稍微增大以匹配参考图片
            data: this.y1Data.map((item, index) => {
              // 计算同比变化百分比
              return ((item - this.y2Data[index]) / this.y2Data[index] * 100).toFixed(2);
            }),
            lineStyle: {
              color: "#FF6660",
              width: 2, // 线条粗细，调整为更精细的宽度
              type: "solid", // 实线
              cap: "round", // 线条端点样式
              join: "round" // 线条连接点样式
            },
            itemStyle: {
              color: "#FF6660", // 数据点填充颜色，与线条颜色一致
              borderColor: "#FF6660", // 边框颜色与填充颜色一致，无对比边框
              borderWidth: 0, // 移除边框，使数据点更简洁
              shadowColor: "rgba(255, 102, 96, 0.4)", // 调整阴影颜色，增强可见性
              shadowBlur: 4, // 适度的阴影模糊
              shadowOffsetX: 0,
              shadowOffsetY: 1 // 轻微的阴影偏移，增加立体感
            },
            emphasis: {
              // 鼠标悬停时的样式
              itemStyle: {
                color: "#FF6660",
                borderColor: "#FF6660",
                borderWidth: 0,
                shadowColor: "rgba(255, 102, 96, 0.6)",
                shadowBlur: 6,
                shadowOffsetX: 0,
                shadowOffsetY: 2,
                scale: 1.2 // 悬停时数据点放大效果
              },
              lineStyle: {
                width: 2.5
              }
            },
            smooth: 0.3, // 添加平滑效果，使线条更自然
            label: {
              show: false
            }
          },
          {
            name: "OPEX费用",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y1Data,
          },
          {
            name: "OPEX费用",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.y1Data,
          },
          {
            name: "OPEX费用",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y1Data,
          },

          {
            name: "去年同期OPEX",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y2Data,
          },
          {
            name: "去年同期OPEX",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.y2Data,
          },
          {
            name: "去年同期OPEX",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y2Data,
          },
        ],
      };
      mychart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.costCompar {
  width: 100%;
  .chart-box {
    width: 95%;
    height: 300px;
  }
}
</style>
