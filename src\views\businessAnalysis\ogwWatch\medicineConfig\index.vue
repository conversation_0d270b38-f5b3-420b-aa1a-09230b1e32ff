<template>
  <div class="medicine-config">
    <!-- 搜索和操作按钮区域 -->
    <div class="search-actions-row">
      <div class="search-section">
        <OgwSearch
          :fields="searchFields"
          v-model="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        ></OgwSearch>
      </div>
      <div class="actions-section">
        <el-button type="primary" @click="addInfo">新增</el-button>
        <el-button type="primary" @click="saveInfo">保存</el-button>
      </div>
    </div>

    <div class="table-container">
      <OgwTable
        ref="ogwTable"
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        @cell-change="handleChange"
        @cell-click="handleCellClick"
        @enter-edit="handleEnterEdit"
      >
        <template #instruction="{ row, $index }">
          <el-button
            v-if="!row.id"
            type="text"
            size="mini"
            @click="deleteRow(row, $index)"
          >
            删除</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="uploadRow(row)"
          >
            上传</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="checkInfo(row)"
            >查看({{
              row.fileInfo ? row.fileInfo.split(",").length : 0
            }})</el-button
          ></template
        >
      </OgwTable>
    </div>
    <FileUpload
      :visible.sync="showUpload"
      :upload-url="`${prefix}/chemicalConfig/upload`"
      :file-types="['.pdf', '.png', '.jpg', '.doc', '.docx']"
      :maxSizeMB="10"
      :fileData="fileData"
      @upload-success="uploadSuccess"
    />
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getTableList,
  updateMedicineConfig,
  getMedicineInfo,
} from "@/api/ogwWatch/medicineConfig.js";
import { getProd } from "@/api/common.js";
export default {
  name: "medicineConfig",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    this.getChemicalNameList();
    this.getProdList();
    this.getTableListInfo();
    this.initializeChemicalTypeColumn();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
      ];
    },
  },
  data() {
    return {
      deviceOptions: [],
      chemicalNameOptions: [],
      chemicalDataMap: {}, // 存储完整的药剂数据映射，用于联动
      loading: false,
      showUpload: false,
      hzNo: "",
      orgList: [],
      prefix: process.env.VUE_APP_PREFIX,
      searchForm: {
        orgId: "",
        deviceNameCode: "",
      },
      columns: [
        {
          label: "平台名称",
          prop: "productionName",
          editable: false,
          options: [],
        },
        {
          label: "药剂名称",
          prop: "chemicalName",
          editable: true,
          options: [],
        },
        {
          label: "药剂型号",
          prop: "chemicalType",
          editable: true, // 改为可编辑，支持下拉选择
          clickable: true, // 保持可点击，用于处理特殊逻辑
          options: [], // 动态更新
        },
        { label: "库存编码", prop: "materialCode", editable: true },
        { label: "推荐加注浓度(ppm)", prop: "reConcentration", editable: true },
        {
          label: "用途",
          prop: "purpose",
          editable: true,
          options: [
            { label: "原油处理", value: "4304030400" },
            { label: "污水处理", value: "4304030100" },
            { label: "注水处理", value: "4304030200" },
            { label: "天然气处理", value: "4304030300" },
            { label: "其他化学药品", value: "4304030500" },
          ],
        },
        {
          label: "费用关注度",
          prop: "attention",
          editable: true,
          options: [
            { label: "重点关注", value: 1 },
            { label: "一般关注", value: 2 },
          ],
        },
        { label: "加注浓度(ppm)", prop: "concentration", editable: true },
        {
          label: "密度(g/cm³)",
          prop: "density",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入数字",
          },
        },
        {
          label: "状态",
          prop: "status",
          editable: true,
          options: [
            { label: "启用", value: 1 },
            { label: "禁用", value: 2 },
          ],
        },
        { label: "药剂说明书", prop: "instruction" },
      ],
      tableData: [],
      updateData: [], //表格更新的数据
      fileData: {},
    };
  },
  methods: {
    handleSearch(value) {
      this.hzNo = value.deviceNameCode;
      this.getTableListInfo();
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
      };
      this.hzNo = "";
      this.getTableListInfo();
    },
    uploadRow(row) {
      this.showUpload = true;
      this.fileData = { id: row.id };
    },
    checkInfo(row) {
      const fileIds = row.fileInfo || null;

      this.$router.push({
        name: "opinionsPre",
        params: { fileIds },
      });
    },
    deleteRow(data, index) {
      this.tableData.splice(index, 1);
      this.updateData = this.updateData.filter((item) => item !== index);
    },
    addInfo() {
      this.columns[0].editable = true;
      // 重置药剂型号列的选项，但保持可编辑状态
      this.columns[2].options = [];
      // 不设置editable为false，让点击事件来控制

      this.tableData.unshift({
        productionName: "",
        chemicalId: "",
        chemicalName: "", // 药剂名称
        chemicalType: "", // 药剂型号
        materialCode: "", // 库存编码
        reConcentration: "",
        purpose: "",
        attention: "",
        concentration: "",
        density: "",
        instruction: "",
        status: 1,
      });
    },
    async getTableListInfo() {
      this.loading = true;
      try {
        const res = await getTableList(this.hzNo || "");
        if (res.code === 200) {
          this.tableData = res.data;
        } else {
          this.tableData = [];
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        this.loading = false;
      }
    },
    async saveInfo() {
      // set集合转成数组
      const targetArr = Array.from(new Set(this.updateData));

      const targetObj = targetArr.map((item) => this.tableData[item]);

      const res = await updateMedicineConfig(targetObj);
      if (res.code === 200) {
        this.$message.success("保存成功");
        this.getTableListInfo();
      }
    },
    handleCellClick({ row, prop, index }) {
      // 如果点击的是药剂型号列
      if (prop === "chemicalType") {
        if (!row.chemicalName) {
          this.$message.warning("请先选择药剂名称");
          return;
        }

        // 先更新选项
        this.updateChemicalTypeOptionsForRow(row, index);

        // 然后进入编辑模式
        this.$nextTick(() => {
          this.$set(this.columns[2], "editable", true);
          this.$refs.ogwTable?.enterEdit({ index, prop });
        });
      }
    },

    // 处理进入编辑模式的事件
    handleEnterEdit({ index, prop }) {
      // 如果是药剂型号列进入编辑模式，动态更新选项
      if (prop === "chemicalType") {
        const row = this.tableData[index];
        const success = this.updateChemicalTypeOptionsForRow(row, index);

        if (!success) {
          return;
        }
      }
    },

    // 更新指定行的药剂型号选项
    updateChemicalTypeOptionsForRow(row, rowIndex) {
      // 如果没有选择药剂名称，提示用户先选择药剂名称
      if (!row.chemicalName) {
        this.$message.warning("请先选择药剂名称");
        // 清空选项
        this.columns[2].options = [];
        return false;
      }

      const selectedChemical = this.chemicalNameOptions.find((item) => {
        return (
          item.label === row.chemicalName || item.value === row.chemicalName
        );
      });

      if (
        selectedChemical &&
        selectedChemical.child &&
        selectedChemical.child.length > 0
      ) {
        // 更新药剂型号的选项
        const options = selectedChemical.child.map((item) => {
          return {
            label: item.chemicalType,
            value: item.chemicalType,
            id: item.id,
          };
        });

        // 动态更新列的选项
        this.$set(this.columns[2], "options", options);

        // 确保列是可编辑的
        this.$set(this.columns[2], "editable", true);

        // 强制更新视图
        this.$forceUpdate();

        return true;
      } else {
        this.$message.warning("未找到对应的药剂型号选项");
        // 清空选项
        this.columns[2].options = [];
        return false;
      }
    },
    handleChange(row) {
      this.updateData.push(row.index);

      // 实现药剂名称联动功能
      if (row.prop === "chemicalName") {
        this.handleChemicalNameChange(row);
      }

      if (row.prop === "productionName") {
        const hzNo = this.deviceOptions.find(
          (item) => item.label === row.value
        ).hzNo;
        this.$set(this.tableData[row.index], "hzNo", hzNo);
      }

      if (row.prop === "chemicalType") {
        console.log("chemicalType", row);
        const chemicalId = this.chemicalNameOptions
          .find(
            (item) =>
              item.label === row.row.chemicalName ||
              item.value === row.row.chemicalName
          )
          .child.find((item) => item.chemicalType === row.value).id;
        this.$set(this.tableData[row.index], "chemicalId", chemicalId);
      }
    },

    // 处理药剂名称变化的联动逻辑
    handleChemicalNameChange({ row, value, index }) {
      this.$set(this.tableData[index], "chemicalType", "");

      const tempRow = { ...row, chemicalName: value };

      this.updateChemicalTypeOptionsForRow(tempRow, index);
    },
    uploadSuccess() {
      this.showUpload = false;
      this.getTableListInfo();
    },

    // 初始化药剂型号列
    initializeChemicalTypeColumn() {
      this.columns[2].options = [];
    },

    // 获取指定行的药剂型号选项
    getChemicalTypeOptionsForRow(row) {
      if (!row.chemicalName) {
        return [];
      }

      const selectedChemical = this.chemicalNameOptions.find((item) => {
        return (
          item.label === row.chemicalName || item.value === row.chemicalName
        );
      });

      if (selectedChemical && selectedChemical.child) {
        return selectedChemical.child.map((item) => {
          return {
            label: item.chemicalType,
            value: item.chemicalType,
            id: item.id,
          };
        });
      }

      return [];
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.deviceOptions = this.orgList
          .map((item) => {
            return item.children.map((c) => {
              return {
                label: c.label,
                value: c.label,
                hzNo: c.value,
              };
            });
          })
          .flat();
        this.searchForm.orgId = this.orgList[0].orgId;
        this.columns[0].options = this.deviceOptions;
      }
    },
    async getChemicalNameList() {
      const res = await getMedicineInfo();
      if (res.code === 200) {
        // 构建药剂选项数据
        this.chemicalNameOptions = res.data.map((item) => {
          return {
            label: item.chemicalName,
            value: item.prdCode,
            child: item.children,
          };
        });
        this.columns[1].options = this.chemicalNameOptions;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-config {
  padding: 20px;
}

// 搜索和操作按钮行
.search-actions-row {
  display: flex;
  align-items: flex-start;
  gap: 20px;

  .search-section {
    flex: 1;
    min-width: 0; // 防止flex项目溢出
  }

  .actions-section {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 18px;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .search-actions-row {
    flex-direction: column;
    gap: 15px;

    .actions-section {
      justify-content: flex-end;
      width: 100%;
    }
  }
}

// 表格容器（间距已在search-actions-row中处理）

[data-theme="dark"] .medicine-config {
  background: #162549;
}

[data-theme="tint"] .medicine-config {
  background: #fff;
}
</style>
