<template>
  <div class="regional-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "regionalChart",
  data() {
    return {
      xData: ["气电海南", "香港中电", "气电广东"],
      y1Data: [6.3, 4.5, 4, 7],
      y2Data: [4.5, 3.6, 3, 6],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {
        color: ["#248EFF", "#7262FD"], //圆柱体颜色
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12,
          },
          formatter: (params) => {
            // 获取X轴标签作为标题
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;

            // 过滤重复的数据系列，只保留每个唯一名称的第一个系列
            const uniqueSeries = [];
            const seenNames = new Set();

            params.forEach((param) => {
              if (!seenNames.has(param.seriesName)) {
                seenNames.add(param.seriesName);
                uniqueSeries.push(param);
              }
            });

            // 遍历过滤后的唯一数据系列
            uniqueSeries.forEach((param) => {
              const unit = "万元"; // 根据Y轴单位设置
              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${param.value} ${unit}
              </div>`;
            });

            return result;
          },
        },
        legend: {
          data: ["本年单价", "去年单价"],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.xData,
            axisTick: {
              alignWithLabel: true,
            },
            nameTextStyle: {
              color: "#ACC2E2", //文本颜色
            },
            axisLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)", //轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2", //轴线文本颜色
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "万元",
            position: "left",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
          },
        ],
        series: [
          {
            name: "本年单价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y1Data,
            markLine: {
              symbol: "none",
              label: {
                position: "start",
                formatter: "{b}",
              },
              lineStyle: {
                color: "#FF6660",
                type: "line",
              },
              data: [
                {
                  yAxis: 3.6,
                },
              ],
            },
          },
          {
            name: "本年单价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: this.y1Data,
          },
          {
            name: "本年单价",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y1Data,
          },

          {
            name: "去年单价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: this.y2Data,
          },
          {
            name: "去年单价",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            data: this.y2Data,
          },
          {
            name: "去年单价",
            type: "bar",
            itemStyle: {
              normal: {
                opacity: 0.7,
              },
            },
            barWidth: "20",
            data: this.y2Data,
          },
        ],
      };
      mychart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.regional-chart {
  width: 100%;
  .chart-box {
    width: 95%;
    height: 300px;
  }
}
</style>
